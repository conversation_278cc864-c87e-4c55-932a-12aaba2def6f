{% extends "base.html" %}

{% block content %}
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h2 class="h2">门票管理</h2>
    <div class="btn-toolbar mb-2 ms-2">
      <a href="/adm/ticket/form/"
         class="btn btn-sm btn-outline-primary">
        <i class="fas fa-plus"></i>
        添加门票
      </a>
    </div>
  </div>

  {% if tickets %}
    <div class="table-responsive">
      <table class="table table-striped table-hover table-bordered align-middle table-sm">
        <thead>
          <tr>
            <th class="px-3 text-center w-5">ID</th>
            <th class="px-3 w-25">标题</th>
            <th class="px-3">展馆</th>
            <th class="px-3 text-center">类型</th>
            <th class="px-3 text-end">最低票价</th>
            <th class="px-3 text-end">最高票价</th>
            <th class="px-3 w-20">操作</th>
          </tr>
        </thead>
        <tbody>
          {% for ticket in tickets %}
            <tr>
              <td class="px-3 text-center">{{ ticket.id }}</td>
              <td class="px-3 fw-semibold">
                <a href="/adm/ticket/detail/?tid={{ ticket.id }}">{{ ticket.title }}</a>
              </td>
              <td class="px-3">
                <a href="/adm/exhihall/detail/?hid={{ ticket.exhihall.id }}">{{ ticket.exhihall.name }}</a>
              </td>
              <td class="px-3 text-center">
                <span class="badge
                             {% if ticket.catg == '预约票' %}
                               bg-success
                             {% else %}
                               bg-primary
                             {% endif %}">{{ ticket.catg }}</span>
              </td>
              <td class="px-3 text-end">
                <strong class="font-monospace">{{ ticket.price_range[0] }}</strong> <span class="text-secondary">¥</span>
              </td>
              <td class="px-3 text-end">
                <strong class="font-monospace text-danger">{{ ticket.price_range[1] }}</strong> <span class="text-secondary">¥</span>
              </td>
              <td class="px-3">
                <div class="btn-group">
                  <a href="/adm/ticket/detail/?tid={{ ticket.id }}"
                     class="btn btn-sm btn-primary">详情</a>
                  <a href="/adm/ticket/form/?tid={{ ticket.id }}"
                     class="btn btn-sm btn-warning">修改</a>
                  <a href="/adm/ticket/delete/?tid={{ ticket.id }}"
                     class="btn btn-sm btn-danger"
                     onclick="return confirm('确定要删除吗？');">删除</a>
                </div>
              </td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  {% else %}
    <div class="alert alert-warning" role="alert">
      <i class="fas fa-exclamation-triangle"></i>
      尚未添加任何门票
    </div>
  {% endif %}
{% endblock content %}
