<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="后台管理系统" />
    <meta name="keywords" content="后台,管理,系统" />
    <title>「华夏漫游」管理系统</title>
    <link rel="icon"
          type="image/png"
          sizes="400x400"
          href="/static/img/favicon.png" />
    <link rel="stylesheet"
          href="https://cdn.staticfile.net/bootstrap/5.3.2/css/bootstrap.min.css" />
    <link rel="stylesheet"
          href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css" />
    <link rel="stylesheet" href="/static/css/styles.css?v={{ ver }}" />
  </head>
  <body>
    <div class="overlay" id="overlay"></div>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
      <div class="container-fluid">
        <button class="navbar-toggler" type="button" id="sidebar-toggler">
          <i class="fa-solid fa-bars"></i>
        </button>
        <nav aria-label="breadcrumb"
             class="d-flex align-items-center ms-3">
          <i class="fa-solid fa-location-dot me-2"></i>
          <ol class="breadcrumb mb-0">
            {% if breadcrumbs %}
              {% for crumb in breadcrumbs %}
                <li class="breadcrumb-item
                           {% if loop.last %}active{% endif %}"
                    {% if loop.last %}aria-current="page"{% endif %}>
                  {% if not loop.last %}
                    <a href="{{ crumb.url }}">{{ crumb.name }}</a>
                  {% else %}
                    <span>{{ crumb.name }}</span>
                  {% endif %}
                </li>
              {% endfor %}
            {% else %}
              <li class="breadcrumb-item active" aria-current="page">
                <a href="/adm/">首页</a>
              </li>
            {% endif %}
          </ol>
        </nav>
        <div class="collapse navbar-collapse">
          <ul class="navbar-nav ms-auto">
            {% if adm and adm.role == '超级管理员' %}
              <li class="nav-item">
                <span class="nav-link text-danger">
                  <i class="fa-solid fa-triangle-exclamation me-2"></i>
                  “超级管理员”权限过大，为避免误操作，建议切换“普通管理员”身份
                </span>
              </li>
            {% elif cfg.DEBUG %}
              <li class="nav-item">
                <span class="nav-link text-danger">
                  <i class="fa-solid fa-triangle-exclamation me-2"></i>当前为测试模式，请勿在线上环境使用
                </span>
              </li>
            {% endif %}
            <li class="nav-item dropdown d-none d-lg-block">
              <a class="nav-link dropdown-toggle"
                 href="#"
                 id="navbarDropdown"
                 role="button"
                 data-bs-toggle="dropdown"
                 aria-expanded="false">
                <i class="fas fa-user me-1"></i>{{ request.user.display_name }}
              </a>
              <ul class="dropdown-menu dropdown-menu-end"
                  aria-labelledby="navbarDropdown">
                <li>
                  <a class="dropdown-item" href="/adm/logout">退出登录</a>
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <div class="main-container">
      <nav class="sidebar" id="sidebar">
        <div class="text-end d-lg-none">
          <button class="btn-close-sidebar" id="sidebar-closer">
            <i class="fa-solid fa-circle-xmark"></i>
          </button>
        </div>
        <div class="sidebar-header">
          <img src="/static/img/blue-white.png"
               alt="Logo"
               class="img-fluid" />
          <h4>华夏漫游管理系统</h4>
        </div>
        <div class="position-sticky">
          <ul class="nav flex-column">
            <li class="nav-header">
              <i class="fas fa-chart-line me-2"></i>数据
            </li>
            <li class="nav-item">
              <a class="nav-link
                        {% if request.url.path == '/adm/' %}active{% endif %}"
                 href="/adm/">
                <i class="fas fa-chart-pie fa-fw mx-2"></i>数据概览
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link
                        {% if 'order' in request.url.path %}active{% endif %}"
                 href="/adm/order/">
                <i class="fas fa-ticket-alt fa-fw mx-2"></i>订单数据
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link
                        {% if 'reservation' in request.url.path %}active{% endif %}"
                 href="/adm/reservation/">
                <i class="fa-solid fa-calendar-days fa-fw mx-2"></i>预约列表
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link
                        {% if 'user' in request.url.path %}active{% endif %}"
                 href="/adm/user/">
                <i class="fas fa-users fa-fw mx-2"></i>用户数据
              </a>
            </li>
            <li class="nav-header">
              <i class="fas fa-screwdriver-wrench me-2"></i>设置
            </li>
            {% if adm and adm.can_manage_settings %}
              <li class="nav-item">
                <a class="nav-link
                          {% if 'settings' in request.url.path %}active{% endif %}"
                   href="/adm/settings/">
                  <i class="fas fa-sliders fa-fw mx-2"></i>全局设置
                </a>
              </li>
            {% endif %}
            <li class="nav-item">
              <a class="nav-link
                        {% if 'exhibition' in request.url.path %}active{% endif %}"
                 href="/adm/exhibition/">
                <i class="fas fa-vr-cardboard fa-fw mx-2"></i>数字展览
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link
                        {% if 'exhihall' in request.url.path %}active{% endif %}"
                 {% if adm and adm.hid %}
                   href="/adm/exhihall/detail/?hid={{ adm.hid }}"
                 {% else %}
                   href="/adm/exhihall/"
                 {% endif %}>
                <i class="fas fa-university fa-fw mx-2"></i>展馆管理
              </a>
            </li>
            {% if adm and adm.can_manage_tickets %}
              <li class="nav-item">
                <a class="nav-link
                          {% if 'ticket' in request.url.path %}active{% endif %}"
                   href="/adm/ticket/">
                  <i class="fas fa-ticket fa-fw mx-2"></i>门票管理
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link
                          {% if 'joint' in request.url.path %}active{% endif %}"
                   href="/adm/joint/">
                  <i class="fas fa-layer-group fa-fw mx-2"></i>联票管理
                </a>
              </li>
            {% endif %}
            {% if adm and adm.can_manage_admins %}
              <li class="nav-header">
                <i class="fas fa-gavel me-2"></i>权限
              </li>
              <li class="nav-item">
                <a class="nav-link
                          {% if 'manager' in request.url.path %}active{% endif %}"
                   href="/adm/manager/">
                  <i class="fas fa-user-shield fa-fw mx-2"></i>人员管理
                </a>
              </li>
            {% endif %}
          </ul>
        </div>
        <div class="sidebar-footer d-lg-none">
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link" href="/adm/logout">
                <i class="fas fa-sign-out-alt fa-fw mx-2"></i>退出登录
              </a>
            </li>
          </ul>
        </div>
      </nav>

      <main class="content bg-sliver">

        {% block content %}
        {% endblock content %}

      </main>
    </div>

    <script src="https://cdn.staticfile.net/bootstrap/5.3.2/js/bootstrap.bundle.min.js"></script>
    <script>
      document.addEventListener('DOMContentLoaded', function () {
        const sidebar = document.getElementById('sidebar');
        const sidebarToggler = document.getElementById('sidebar-toggler');
        const sidebarCloser = document.getElementById('sidebar-closer');
        const overlay = document.getElementById('overlay');

        function closeSidebar() {
          sidebar.classList.remove('show');
          overlay.classList.remove('show');
        }

        sidebarToggler.addEventListener('click', function () {
          sidebar.classList.toggle('show');
          overlay.classList.toggle('show');
        });

        sidebarCloser.addEventListener('click', closeSidebar);
        overlay.addEventListener('click', closeSidebar);
      });
    </script>

    {% block ext_js %}
    {% endblock ext_js %}

  </body>
</html>
