{% extends "base.html" %}

{% block content %}
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h2 class="h2">预约列表</h2>
  </div>

  <!-- 筛选表单 -->
  <form method="get" class="row g-3 mb-3">
    {% if halls %}
      <div class="col-auto">
        <select name="hid"
                id="hid-filter"
                class="form-select"
                onchange="this.form.submit()">
          <option value="">所有展馆</option>
          {% for hall in halls %}
            <option value="{{ hall.id }}"
                    {% if hall.id == current_hid %}selected{% endif %}>{{ hall.name }}</option>
          {% endfor %}
        </select>
      </div>
    {% endif %}
    <div class="col-auto date-input-wrapper">
      <input type="date"
             name="date"
             class="form-control"
             value="{{ date or '' }}"
             onchange="this.form.submit()" />
    </div>
  </form>

  <style>
    /* 给日期输入框的容器设置相对定位 */
    .col-auto {
      position: relative;
    }

    /* 核心：当 input[type=date] 的值为空时，它不匹配 :valid 伪类 */
    /* 我们可以利用这一点，通过 ::before 伪元素来创建自定义的占位符 */
    input[type="date"]::before {
      content: attr(placeholder);
      position: absolute;
      top: 50%;
      left: 20px; /* 根据输入框的内边距调整 */
      transform: translateY(-50%);
      color: #6c757d; /* 占位符颜色 */
      pointer-events: none; /* 允许点击穿透到输入框 */
    }

    /* 当用户选择了日期，输入框变为 :valid 状态，此时隐藏我们的自定义占位符 */
    input[type="date"]:valid::before {
      display: none;
    }
  </style>

  {% if orders %}
    <div class="table-responsive">
      <table class="table table-striped table-hover table-bordered table-sm align-middle">
        <thead>
          <tr>
            <th class="px-3">门票名称</th>
            <th class="px-3">用户手机</th>
            <th class="px-3">预约时间</th>
            <th class="px-3">门票类型</th>
            <th class="px-3 text-center">数量</th>
            {% if adm and adm.can_checkin_orders %}<th class="px-3 w-10">操作</th>{% endif %}
          </tr>
        </thead>
        <tbody>
          {% for order in orders %}
            <tr>
              <td class="px-3">
                <a href="/adm/order/detail/?order_id={{ order.id }}">{{ order.ticket.title }}</a>
              </td>
              <td class="px-3">
                <a href="/adm/user/detail/?user_id={{ order.uid }}">{{ order.phone }}</a>
              </td>
              <td class="px-3">{{ cn_date(order.ap_time, 'd') }}</td>
              <td class="px-3">{{ order.grade + order.timeslot }}</td>
              <td class="px-3 text-center">{{ order.quantity }}</td>
              <td class="px-3">
                {% if adm and adm.can_checkin_orders and order.status == '待使用' %}
                  <div class="btn-group">
                    <a href="/adm/order/detail/?order_id={{ order.id }}"
                       class="btn btn-sm btn-primary w-10">核销</a>
                  </div>
                {% endif %}
              </td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  {% else %}
    <div class="alert alert-warning" role="alert">
      <i class="fas fa-exclamation-triangle"></i>
      暂无预约
    </div>
  {% endif %}

  <!-- 分页 -->
  <nav aria-label="Page navigation">
    <ul class="pagination justify-content-center">
      {% if page > 1 %}
        <li class="page-item">
          <a class="page-link"
             href="?page={{ page - 1 }}&date={{ date or '' }}&hid={{ current_hid or '' }}">上一页</a>
        </li>
      {% else %}
        <li class="page-item disabled">
          <a class="page-link" href="#">上一页</a>
        </li>
      {% endif %}

      {% for p in range(1, total_pages + 1) %}
        <li class="page-item {% if p == page %}active{% endif %}">
          <a class="page-link"
             href="?page={{ p }}&date={{ date or '' }}&hid={{ current_hid or '' }}">{{ p }}</a>
        </li>
      {% endfor %}

      {% if page < total_pages %}
        <li class="page-item">
          <a class="page-link"
             href="?page={{ page + 1 }}&date={{ date or '' }}&hid={{ current_hid or '' }}">下一页</a>
        </li>
      {% else %}
        <li class="page-item disabled">
          <a class="page-link" href="#">下一页</a>
        </li>
      {% endif %}
    </ul>
  </nav>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const dateInput = document.querySelector('input[name="date"]');
      const wrapper = dateInput.parentElement;

      function updatePlaceholder() {
        if (dateInput.value) {
          wrapper.classList.add('has-value');
        } else {
          wrapper.classList.remove('has-value');
        }
      }

      // Initial check
      updatePlaceholder();

      // Update on change
      dateInput.addEventListener('change', updatePlaceholder);
    });
  </script>
{% endblock content %}
