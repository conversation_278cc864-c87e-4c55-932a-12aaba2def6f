{% extends "base.html" %}

{% block content %}
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h2 class="h2 text-primary">{{ exhibition.name }}</h2>
    {% if adm and adm.can_manage_exhibitions %}
      <div class="btn-toolbar mb-2 ms-2">
        <a href="/adm/exhibition/form/?eid={{ exhibition.id }}"
           class="btn btn-sm btn-outline-primary">
          <i class="fas fa-edit"></i>
          修改
        </a>
      </div>
    {% endif %}
  </div>

  <div class="row justify-content-around">
    <div class="col-md-2 text-center">
      <h5>缩略图</h5>
      <hr />
      <img src="{{ exhibition.thumbnail }}"
           alt="{{ exhibition.name }}"
           class="img-fluid rounded" />
    </div>

    <div class="col-md-6 text-center">
      <h5>概览图</h5>
      <hr />
      <img src="{{ exhibition.banner }}"
           alt="{{ exhibition.name }}"
           class="img-fluid rounded" />
    </div>

    <div class="col-md-10 text-center mt-5">
      <h5>详情图</h5>
      <hr />
      {% for detail_img in exhibition.details %}
        <img src="{{ detail_img }}"
             alt="{{ exhibition.name }} 详情图"
             class="img-fluid rounded" />
      {% endfor %}
    </div>
  </div>
{% endblock content %}
