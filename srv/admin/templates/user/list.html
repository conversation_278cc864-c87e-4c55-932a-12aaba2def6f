{% extends "base.html" %}

{% block content %}
  <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h2 class="h2">用户数据</h2>
    {% if adm and adm.can_create_users %}
      <div class="btn-toolbar mb-2 ms-2">
        <a href="/adm/user/form/"
           class="btn btn-sm btn-outline-primary">
          <i class="fas fa-plus"></i>
          新增用户
        </a>
      </div>
    {% endif %}
  </div>

  <!-- 搜索表单 -->
  <form method="get" class="row g-3 mb-3">
    <div class="col-auto">
      <input type="text"
             name="q"
             class="form-control"
             placeholder="用户名/手机号"
             value="{{ q or '' }}" />
    </div>
    <div class="col-auto">
      <button type="submit" class="btn btn-primary">搜索</button>
    </div>
  </form>

  {% if users %}
    <div class="table-responsive">
      <table class="table table-striped table-hover table-bordered table-sm align-middle">
        <thead>
          <tr>
            <th class="px-3 text-center w-5">ID</th>
            <th class="px-3 w-25">用户名</th>
            <th class="px-3">手机号</th>
            <th class="px-3">注册时间</th>
            {% if cfg.DEBUG %}<th class="px-3">微信ID</th>{% endif %}
            {% if adm and adm.can_edit_users %}<th class="px-3 w-20">操作</th>{% endif %}
          </tr>
        </thead>
        <tbody>
          {% for user in users %}
            <tr>
              <td class="px-3 text-center">
                <span class="{% if user.is_adm %}
                               badge rounded-pill text-bg-success
                             {% endif %}">{{ user.id }}</span>
              </td>
              <td class="px-3">
                <a href="/adm/user/detail/?user_id={{ user.id }}">{{ user.name }}</a>
              </td>
              <td class="px-3 font-monospace">{{ user.phone or '未绑定' }}</td>
              <td class="px-3">{{ iso_date(user.created, 'm') }}</td>
              {% if cfg.DEBUG %}<td class="px-3 font-monospace">{{ user.openid }}</td>{% endif %}
              <td class="px-3">
                <div class="btn-group">
                  {% if adm and adm.can_edit_users %}
                    <a href="/adm/user/form/?user_id={{ user.id }}"
                       class="btn btn-sm btn-outline-danger">修改</a>
                  {% endif %}
                  {% if adm and adm.can_delete_users and cfg.DEBUG %}
                    <a href="/adm/user/delete/?user_id={{ user.id }}"
                       class="btn btn-sm btn-danger"
                       onclick="return confirm('确定要删除吗？');">删除</a>
                  {% endif %}
                </div>
              </td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <nav aria-label="Page navigation">
      <ul class="pagination justify-content-center">
        {% if page > 1 %}
          <li class="page-item">
            <a class="page-link"
               href="?page={{ page - 1 }}&q={{ q or '' }}">上一页</a>
          </li>
        {% else %}
          <li class="page-item disabled">
            <a class="page-link" href="#">上一页</a>
          </li>
        {% endif %}

        {% for p in range(1, total_pages + 1) %}
          <li class="page-item {% if p == page %}active{% endif %}">
            <a class="page-link" href="?page={{ p }}&q={{ q or '' }}">{{ p }}</a>
          </li>
        {% endfor %}

        {% if page < total_pages %}
          <li class="page-item">
            <a class="page-link"
               href="?page={{ page + 1 }}&q={{ q or '' }}">下一页</a>
          </li>
        {% else %}
          <li class="page-item disabled">
            <a class="page-link" href="#">下一页</a>
          </li>
        {% endif %}
      </ul>
    </nav>
  {% else %}
    <div class="alert alert-warning" role="alert">
      <i class="fas fa-exclamation-triangle"></i>
      暂无用户
    </div>
  {% endif %}
{% endblock content %}
