import datetime
import math

from fastapi import Depends, Form, Query, Response
from fastapi.responses import RedirectResponse as Redirect
from tortoise.expressions import Q

from admin import schemas as sch
from admin.http import render_html, router
from admin.permissions import require_order_delete, require_order_edit
from apps.exhi.models import <PERSON>hiHall, Ticket
from apps.order.models import Order, OrderStatus
from apps.order.schemas import OrderFilter
from apps.user.models import User
from config import PAGE_SIZE, TZ
from libs.state import State

__all__ = ['order_delete', 'order_detail', 'order_form', 'order_list', 'order_save', 'reservation_list']


@router.get('/order/')
async def order_list(
    page: int = Query(1, ge=1),
    q: str | None = Query(None),
    status: str = Query(''),
    hid: str = Query(''),
) -> Response:
    """订单列表页，按创建时间倒序"""
    cur_adm = State.get('user')
    query = Order.all()

    hid_val = int(hid) if hid.isdigit() else None
    current_hid = cur_adm.hid or hid_val
    if current_hid:
        query = query.filter(hid=current_hid)

    if q:
        query = query.filter(Q(trade_no__icontains=q) | Q(phone__icontains=q))
    if status:
        f = OrderFilter(status)
        query = query.filter(f.query)

    total_count = await query.count()
    total_pages = math.ceil(total_count / PAGE_SIZE)
    orders = await query.limit(PAGE_SIZE).offset((page - 1) * PAGE_SIZE).order_by('-created')
    halls = await ExhiHall.all() if not cur_adm.hid else []

    # N+1 问题, 后续可以优化
    for order in orders:
        await order.prefetch()
    context = {
        'orders': orders,
        'page': page,
        'total_pages': total_pages,
        'q': q,
        'statuses': OrderFilter,
        'current_status': status,
        'halls': halls,
        'current_hid': current_hid,
    }
    return render_html('order/list.html', context)


@router.get('/order/detail/')
async def order_detail(order_id: int) -> Response:
    """订单详情页"""
    order = await Order.get(id=order_id)
    await order.prefetch()
    return render_html('order/detail.html', {'order': order})


@router.get('/order/form/')
async def order_form(order_id: int | None = Query(None)) -> Response:
    """订单创建和编辑页"""
    order, is_edit = (await Order.get(id=order_id), True) if order_id else (None, False)
    users = await User.all()
    from apps.exhi.schemas import TicketCategory

    context = {
        'order': order,
        'statuses': OrderStatus,
        'categories': TicketCategory,
        'users': users,
        'is_edit': is_edit,
    }
    return render_html('order/form.html', context)


@router.post('/order/save/')
@require_order_edit()
async def order_save(
    form: sch.OrderForm = Depends(sch.OrderForm.load),
    order_id: int | None = Form(None),
) -> Response:
    """处理订单创建和编辑表单"""
    update_data = form.model_dump(exclude_unset=True, exclude_none=True)
    if order_id:
        await Order.filter(id=order_id).update(**update_data)
        await Order.clear_cache(order_id)
    else:
        tk = await Ticket.get(id=form.tid)
        update_data['archive'] = await tk.detail()
        await Order.create(**update_data)

    return Redirect(url='/adm/order/', status_code=303)


@router.get('/order/delete/')
@require_order_delete()
async def order_delete(order_id: int) -> Response:
    """删除指定订单"""
    await (await Order.get(id=order_id)).delete()
    return Redirect(url='/adm/order/', status_code=303)


@router.get('/reservation/')
async def reservation_list(
    hid: str = Query(''),
    date: str = Query(''),
    page: int = Query(1, ge=1),
) -> Response:
    """预约列表页"""
    cur_adm = State.get('user')

    query = Order.filter(catg='预约票', status=OrderStatus.paid)
    if current_hid := cur_adm.hid or (int(hid) if hid.isdigit() else None):
        query = query.filter(hid=current_hid)

    today = datetime.datetime.now(TZ).replace(hour=0, minute=0, second=0, microsecond=0)
    if not date:
        query = query.filter(ap_time__gte=today)
    else:
        selected_day = datetime.datetime.fromisoformat(date).replace(tzinfo=TZ)
        next_day = selected_day + datetime.timedelta(days=1)
        query = query.filter(ap_time__gte=selected_day, ap_time__lt=next_day)

    total_count = await query.count()
    total_pages = math.ceil(total_count / PAGE_SIZE)
    orders = await query.limit(PAGE_SIZE).offset((page - 1) * PAGE_SIZE).order_by('ap_time')

    for order in orders:
        await order.prefetch()

    context = {
        'orders': orders,
        'today': today.date().isoformat(),
        'date': date,
        'current_hid': current_hid,
        'halls': await ExhiHall.all() if not cur_adm.hid else [],
        'page': page,
        'total_pages': total_pages,
    }
    return render_html('order/reservation.html', context)
