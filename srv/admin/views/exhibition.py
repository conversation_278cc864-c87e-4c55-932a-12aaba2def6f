from fastapi import Depends, Query, Response
from fastapi.responses import RedirectResponse as Redirect

from admin.http import render_html, router
from admin.schemas import ExhibitionForm
from apps.exhi.models import Exhibition
from config import BANNER_PREFIX, DETAIL_PREFIX, THUMB_PREFIX
from libs.utils import save_upfile

__all__ = ['exhibition_delete', 'exhibition_detail', 'exhibition_form', 'exhibition_list', 'exhibition_save']


@router.get('/exhibition/')
async def exhibition_list() -> Response:
    """展览列表页"""
    exhibitions = await Exhibition.all()
    return render_html('exhibition/list.html', {'exhibitions': exhibitions})


@router.get('/exhibition/detail/')
async def exhibition_detail(eid: int) -> Response:
    """展览详情页"""
    exhibition = await Exhibition.get(id=eid)
    return render_html('exhibition/detail.html', {'exhibition': exhibition})


@router.get('/exhibition/form/')
async def exhibition_form(eid: int | None = Query(None)) -> Response:
    """展览创建和编辑页"""
    exhibition, is_edit = (await Exhibition.get_or_none(id=eid), True) if eid else (None, False)
    return render_html('exhibition/form.html', {'exhibition': exhibition, 'is_edit': is_edit})


@router.post('/exhibition/save/')
async def exhibition_save(form: ExhibitionForm = Depends(ExhibitionForm.as_form)) -> Response:
    """处理展览创建和编辑表单"""
    update_data = {}
    if form.name:
        update_data['name'] = form.name

    if form.thumbnail and hasattr(form.thumbnail, 'filename') and form.thumbnail.filename:
        update_data['thumbnail'] = await save_upfile(form.thumbnail, THUMB_PREFIX)
    elif form.thumbnail_path:
        update_data['thumbnail'] = form.thumbnail_path

    if form.banner and hasattr(form.banner, 'filename') and form.banner.filename:
        update_data['banner'] = await save_upfile(form.banner, BANNER_PREFIX)
    elif form.banner_path:
        update_data['banner'] = form.banner_path

    new_details = form.details_paths.copy()
    for upfile in form.details:
        if hasattr(upfile, 'filename') and upfile.filename:
            new_details.append(await save_upfile(upfile, DETAIL_PREFIX))
    if new_details or not form.eid:  # 如果是新建或有更新时才写入
        update_data['details'] = new_details  # type: ignore

    if form.eid:
        await Exhibition.filter(id=form.eid).update(**update_data)
        await Exhibition.clear_cache(form.eid)
        new_eid = form.eid
    else:
        new_eid = (await Exhibition.create(**update_data)).id  # type: ignore

    return Redirect(url=f'/adm/exhibition/detail/?eid={new_eid}', status_code=303)


@router.get('/exhibition/delete/')
async def exhibition_delete(eid: int) -> Response:
    """删除指定展览"""
    await (await Exhibition.get(id=eid)).delete()
    return Redirect(url='/adm/exhibition/', status_code=303)
