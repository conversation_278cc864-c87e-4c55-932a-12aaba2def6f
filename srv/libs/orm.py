from collections.abc import Callable, Iterable, Set
from functools import wraps
from itertools import islice
from typing import Any, Self

from tortoise import Tortoise
from tortoise.backends.base.client import BaseDBAsyncClient
from tortoise.exceptions import DoesNotExist
from tortoise.expressions import Q
from tortoise.fields import IntField
from tortoise.manager import Manager
from tortoise.models import Model as TortoiseModel
from tortoise.queryset import QuerySet

from config import CACHE_MAX_AGE, DATABASE
from libs.cache import redis

FieldNames = Iterable[str]

EMPTY = '__empty__'


def ibatch(iterable: Any, batch_size: int) -> Iterable[Any]:
    """分批迭代器"""
    it = iter(iterable)
    while batch := list(islice(it, batch_size)):
        yield batch


async def init_db():
    await Tortoise.init(config=DATABASE)


class CachedQuerySet(QuerySet):
    """支持缓存同步的QuerySet"""

    async def update(self, **kwargs) -> int:  # type: ignore
        """重写update方法，同步缓存"""
        if ids := await self.values_list('id', flat=True):
            try:
                return await super().update(**kwargs)
            finally:
                await self.model.clear_cache(*ids)  # 删除缓存
        return 0

    async def delete(self) -> int:  # type: ignore
        """重写delete方法，同步缓存"""
        if ids := await self.values_list('id', flat=True):
            try:
                return await super().delete()
            finally:
                await self.model.clear_cache(*ids)  # 删除缓存
        return 0

    async def _prefetch_all(self):
        """预加载所有关联对象 (TODO: 改进 ID 标记策略，包括 xxx_id, xxx_ids)"""
        objs = await super().all()
        for attr, model_cls in self.model.__annotations__.items():
            if issubclass(model_cls, Model):
                related_ids = [getattr(obj, f'{attr[0]}id') for obj in objs]
                related_objs = {obj.id: obj for obj in await model_cls.filter(id__in=related_ids)}
                for obj in objs:
                    setattr(obj, attr, related_objs.get(getattr(obj, f'{attr[0]}id')))
        return objs


class CachedManager(Manager):
    """支持缓存同步的Manager"""

    def get_queryset(self) -> CachedQuerySet:
        """返回自定义的QuerySet"""
        return CachedQuerySet(self._model)  # type: ignore


class Model(TortoiseModel):
    """Model 基类"""

    id = IntField(primary_key=True)

    objects = CachedManager()  # 使用自定义Manager

    class Meta:  # type: ignore
        abstract = True

    def __str__(self) -> str:
        return f'{self.__class__.__name__}(id={self.id})'

    def __repr__(self) -> str:
        return f'{self.__class__.__name__}(id={self.id})'

    def __format__(self, format_spec: str) -> str:
        return self.__str__().__format__(format_spec)

    @classmethod
    async def aggregate(cls, query: str) -> list[dict]:
        """执行原生聚合 SQL 查询"""
        conn = Tortoise.get_connection('default')
        return await conn.execute_query_dict(query)

    @classmethod
    def _db_queryset(  # type: ignore
        cls,
        using_db: BaseDBAsyncClient | None = None,
        for_write: bool = False,
    ) -> QuerySet[Self]:
        db = using_db or cls._choose_db(for_write)
        return cls.objects.get_queryset().using_db(db)

    @classmethod
    def _cache_key(cls, pk: int) -> str:
        """生成统一的缓存键"""
        return f'{cls.__name__}::{pk}'

    async def set_cache(self):
        """设置 Model 对象缓存"""
        key = self._cache_key(self.id)
        await redis.set(key, self, CACHE_MAX_AGE)

    async def del_cache(self):
        """删除 Model 对象缓存"""
        key = self._cache_key(self.id)
        return await redis.delete(key)

    @classmethod
    async def rebuild_cache(cls, *primary_keys: int):
        """重建 Model 对象缓存"""
        # 获取所有主键
        if not primary_keys:
            keys = await redis.keys(f'{cls.__name__}::*')
            primary_keys = tuple(int(key.split('::')[-1]) for key in keys)

        if not primary_keys:
            return

        for kilo_primary_keys in ibatch(primary_keys, 1000):
            # 批量获取对象并保存
            objs = await cls.filter(id__in=kilo_primary_keys)
            kilo_keys = {cls._cache_key(obj.id): obj for obj in objs}
            await redis.mset(kilo_keys)  # type: ignore
            # 重设过期时间
            pipeline = redis.pipeline()
            for key in kilo_keys:
                pipeline.expire(key, CACHE_MAX_AGE)
            await pipeline.execute()

    @classmethod
    async def clear_cache(cls, *primary_keys: int):
        """清空 Model 对象缓存"""
        if not primary_keys:
            return await redis.del_pattern(f'{cls.__name__}::*')
        keys = [cls._cache_key(pk) for pk in primary_keys]
        return await redis.delete(*keys)

    @classmethod
    def fields(cls):
        """获取 Model 对象的字段"""
        return cls._meta.fields.copy()

    @classmethod
    async def get_from_db(cls, *args: Q, using_db: BaseDBAsyncClient | None = None, **kwargs: Any) -> Self:  # type: ignore
        """获取 Model 对象"""
        obj = await super().get(*args, using_db=using_db, **kwargs)
        await obj.set_cache()
        return obj

    @classmethod
    async def get(cls, *args: Q, using_db: BaseDBAsyncClient | None = None, **kwargs: Any) -> Self:  # type: ignore
        """获取 Model 对象"""
        pk = kwargs.get('pk') or kwargs.get('id')
        if pk is not None:
            key = cls._cache_key(pk)
            obj = await redis.get(key)
            if isinstance(obj, cls):
                await redis.expire(key, CACHE_MAX_AGE)
                return obj
            if obj is None:
                try:
                    return await cls.get_from_db(*args, using_db=using_db, **kwargs)
                except Exception as e:
                    await redis.set(key, EMPTY, 60)  # 缓存空对象防止穿透
                    raise DoesNotExist(cls) from e
            if obj == EMPTY:
                raise DoesNotExist(cls)
        return await cls.get_from_db(*args, using_db=using_db, **kwargs)

    async def save(self, *args, **kwargs) -> None:
        """保存 Model 对象"""
        await super().save(*args, **kwargs)
        await self.set_cache()

    async def delete(self, *args, **kwargs):
        """删除 Model 对象"""
        try:
            return await super().delete(*args, **kwargs)
        finally:
            await self.del_cache()

    @classmethod
    def filter(cls, *args: Q, **kwargs: Any) -> QuerySet[Self]:  # type: ignore
        """
        Generates a QuerySet with the filter applied.

        :param args: Q functions containing constraints. Will be AND'ed.
        :param kwargs: Simple filter constraints.
        """
        return cls.objects.get_queryset().filter(*args, **kwargs)

    @classmethod
    async def bulk_update(  # type:ignore
        cls,
        objects: Iterable[Self],
        fields: Iterable[str],
        batch_size: int | None = None,
        using_db: BaseDBAsyncClient | None = None,
    ):
        """批量更新对象，同步缓存"""
        result = await super().bulk_update(objects, fields, batch_size, using_db)

        # 批量更新缓存
        if cache_data := {cls._cache_key(obj.id): obj for obj in objects if obj.id}:
            try:
                # 批量设置缓存和过期时间
                if len(cache_data) > 1000:
                    for batch_data in ibatch(cache_data.items(), 1000):
                        await redis.mset(dict(batch_data))
                else:
                    await redis.mset(cache_data)  # type: ignore
                async with redis.pipeline() as pipeline:  # type: ignore
                    for key in cache_data:
                        pipeline.expire(key, CACHE_MAX_AGE)
                    await pipeline.execute()
            except Exception:
                # 缓存更新失败时清除相关缓存确保一致性
                await cls.clear_cache(*[obj.id for obj in objects if obj.id])

        return result

    async def prefetch(self, *args: str) -> Self:
        """预加载关联属性"""
        feilds: Set = self.__annotations__.keys() & set(args) if args else self.__annotations__.keys()
        for field in feilds:
            if not hasattr(self, field) and hasattr(self, f'load_{field}'):
                await getattr(self, f'load_{field}')()
        return self

    def to_dict(
        self,
        only: FieldNames | None = None,
        exclude: FieldNames | None = None,
        extra: FieldNames | None = None,
    ) -> dict[str, Any]:
        keys = set(only or self._meta.fields)
        if exclude is not None:
            keys -= set(exclude)
        if extra is not None:  # 追加字段, 常为 property 属性
            keys |= set(extra)

        return {key: getattr(self, key) for key in keys}


def relate_cache(method: Callable):
    """关联对象装饰器"""

    @wraps(method)
    async def wrapper(self):
        model_obj = await method(self)
        attr_name = method.__name__[5:]
        setattr(self, attr_name, model_obj)
        return model_obj

    return wrapper
