import logging
import sys
import traceback
from asyncio.coroutines import iscoroutinefunction

from fastapi import FastAPI, HTTPException, Request, Response
from pydantic import ValidationError
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint

errlog = logging.getLogger('error')


class BaseMiddleware(BaseHTTPMiddleware):
    """中间件基类"""

    async def attempt_call(self, method_name, *args, **kwargs) -> Response | None:  # type: ignore
        """尝试调用钩子方法"""
        method = getattr(self, method_name, None)
        if callable(method):
            if iscoroutinefunction(method):
                return await method(*args, **kwargs)
            else:
                return method(*args, **kwargs)  # type: ignore

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """中间件分发"""
        # Process request
        response = await self.attempt_call('process_request', request)

        # Call next Middleware or ViewFunction
        if response is None:
            response = await call_next(request)

        # Process response
        return await self.attempt_call('process_response', request, response) or response


class ExceptionMiddleware(BaseHTTPMiddleware):
    """异常处理中间件"""

    def print_local_traceback(self):
        """打印本地的异常堆栈"""
        exc_type, exc_value, exc_tb = sys.exc_info()
        if exc_type is None or exc_value is None or exc_tb is None:
            return
        tb_exc = traceback.TracebackException(exc_type, exc_value, exc_tb, capture_locals=True)

        for frame in tb_exc.stack.copy():
            if 'lib/python' in frame.filename or '/site-packages/' in frame.filename:
                tb_exc.stack.remove(frame)
        message = ''.join(tb_exc.format(chain=False))
        errlog.error(message)

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """处理异常"""
        try:
            return await call_next(request)
        except Exception:
            self.print_local_traceback()
            return Response(content='Internal Server Error', status_code=500)


async def validation_exception_handler(request: Request, exc: ValidationError) -> HTTPException:
    """全局 ValidationError 处理器，确保返回标准的 422 JSON 响应"""
    errors = [{'loc': error['loc'], 'msg': error['msg'], 'type': error['type']} for error in exc.errors()]
    raise HTTPException(status_code=422, detail=errors)


def register_middlewares(app: FastAPI, *middlewares) -> None:
    """注册中间件"""
    for middleware in middlewares[::-1]:
        app.add_middleware(middleware)
