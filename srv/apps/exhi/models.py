import datetime
from functools import cached_property
from typing import ClassVar, Self

from pypinyin import lazy_pinyin
from tortoise import fields
from tortoise.expressions import Q
from tortoise.queryset import QuerySet
from tortoise.transactions import atomic

from apps.exhi.schemas import TicketCategory as Catg
from config import PAGE_SIZE
from libs.orm import Model, relate_cache
from libs.utils import up_url


class Exhibition(Model):
    """VR主题展"""

    name = fields.CharField(max_length=32, unique=True, description='展览名称')
    thumbnail = fields.CharField(max_length=256, null=False, description='缩略图 URL')
    banner = fields.Char<PERSON>ield(max_length=256, null=False, description='概览图 URL')
    details: list[str] = fields.JSONField(default=[], description='详情图 URL 列表')  # type: ignore

    class Meta:  # type: ignore
        ordering: ClassVar[list[str]] = ['id']

    @atomic()
    async def delete(self, *args, **kwargs):  # type: ignore
        """删除展览, 同时删除与之关联的展览"""
        query = Ticket.filter(eid=self.id)
        tids = await query.values_list('id')
        # 删除关联的联票
        for tid in tids:
            await Joint.filter(tids__contains=tid).delete()
        # 删除关联的普通门票
        await query.delete()
        return await super().delete(*args, **kwargs)


class ExhiHall(Model):
    """展馆"""

    name = fields.CharField(max_length=32, null=False, unique=True, description='展馆名称')
    city = fields.CharField(max_length=16, null=False, description='所在城市')
    addr = fields.CharField(max_length=128, null=False, description='详细地址')
    notice = fields.TextField(default='', null=False, description='入馆公告')
    appid = fields.CharField(max_length=32, null=True, description='门店 AppID')
    mchid = fields.CharField(max_length=32, null=True, description='门店商户号')

    class Meta:  # type: ignore
        ordering: ClassVar[list[str]] = ['id']

    @cached_property
    def city_py(self) -> str:
        return ''.join(ct.title() for ct in lazy_pinyin(self.city))

    @atomic()
    async def delete(self, *args, **kwargs):  # type: ignore
        """删除展馆, 同时删除与之关联的展览门票"""
        await Ticket.filter(hid=self.id).delete()
        await Joint.filter(hid=self.id).delete()
        return await super().delete(*args, **kwargs)


class BaseTicket(Model):
    """展览门票"""

    hid = fields.IntField(db_index=True, null=False, description='展馆 ID')
    title = fields.CharField(max_length=64, null=False, unique=True, description='展览标题')
    start = fields.DateField(null=True, description='开始时间')
    end = fields.DateField(null=True, description='结束时间')
    catg = fields.CharEnumField(Catg, max_length=8, default=Catg.ticket, description='门票类型')

    # 价格列表示例：
    #  { '全通': [ ['单人', 100.0], ['双人', 200.0], ... ],
    #    '平日': [ ['家庭', 200.0], ['双人', 200.0], ... ], ... }
    # 正常情况每一项票档为两个值：名称、价格，如果存在第三个值，则为失效时间
    prices: dict[str, list[tuple[str, float]]] = fields.JSONField(default={}, description='价格列表')  # type: ignore

    exhihall: ExhiHall

    class Meta:  # type: ignore
        abstract = True

    @relate_cache
    async def load_exhihall(self) -> ExhiHall:
        """展馆"""
        return await ExhiHall.get(id=self.hid)

    def price_of(self, timeslot: str, grade: str) -> float:
        """获取门票价格"""
        if timeslot not in self.prices:
            return -1
        for price_info in self.prices[timeslot]:
            if len(price_info) != 2:
                continue
            name, price = price_info
            if name == grade:
                return price
        return -1

    @cached_property
    def price_range(self) -> tuple[float, float]:
        """价格区间"""
        if not self.prices:
            return 0.0, 0.0
        values = [price for items in self.prices.values() for _, price in items]
        return min(values), max(values)

    async def archive(self, timeslot: str, grade: str):
        """归档门票信息"""
        await self.prefetch()
        attrs = [
            ('id', self.id),
            ('title', self.title),
            ('catg', self.catg.value),
            ('exhihall', self.exhihall.name),
            ('addr', self.exhihall.addr),
            ('start', self.start),
            ('end', self.end),
            ('timeslot', timeslot),
            ('grade', grade),
            ('price', self.price_of(timeslot, grade)),
        ]
        if exhi := getattr(self, 'exhibition', None):
            attrs.extend([
                ('thumbnail', exhi.thumbnail),
                ('theme', exhi.name),
            ])
        elif tickets := getattr(self, 'tickets', None):
            attrs.extend([
                ('thumbnail', self.thumbnail),  # type: ignore
                ('tickets', [tk.title for tk in tickets]),
            ])

        return attrs

    @classmethod
    def normal(cls):
        return cls.filter(catg=Catg.ticket)

    @classmethod
    def in_times(cls, **kwargs) -> QuerySet[Self]:
        """在展期内的票"""
        today = datetime.date.today()
        query = (
            Q(start__lte=today, end__gte=today)
            | Q(start__isnull=True, end__gte=today)
            | Q(start__isnull=True, end__isnull=True)
        )
        return cls.filter(query, **kwargs)

    @classmethod
    async def in_city(cls, city: str, order: str = '-id', limit: int = PAGE_SIZE, offset: int = 0) -> list[Self]:
        """按城市筛选"""
        table = cls._meta.db_table
        today = datetime.date.today()
        order, aod = (order[1:], 'DESC') if order[0] == '-' else (order, 'ASC')
        return await cls.raw(f"""
            SELECT {table}.*
            FROM {table} JOIN exhihall
            ON {table}.hid = exhihall.id
            WHERE exhihall.city like '{city:s}%'
            AND {table}.start <= '{today:%Y-%m-%d}'
            AND {table}.end >= '{today:%Y-%m-%d}'
            ORDER BY {order:s} {aod:s}
            LIMIT {limit:d}
            OFFSET {offset:d};
        """)  # type: ignore # noqa: S608


class Ticket(BaseTicket):
    """展览门票"""

    eid = fields.IntField(db_index=True, null=False, description='展览 ID')

    # 声明缓存字段
    exhihall: ExhiHall
    exhibition: Exhibition

    class Meta:  # type: ignore
        ordering: ClassVar[list[str]] = ['id']

    @relate_cache
    async def load_exhibition(self) -> Exhibition:
        """展票类型列表"""
        return await Exhibition.get(id=self.eid)

    async def sample(self):
        """展览概览"""
        await self.prefetch()
        return {
            'id': self.id,
            'title': self.title,
            'prices': self.price_range,
            'theme': self.exhibition.name,
            'thumbnail': up_url(self.exhibition.thumbnail),
        }

    async def detail(self):
        """展览详情"""
        await self.prefetch()
        return {
            'id': self.id,
            'title': self.title,
            'prices': self.price_range,
            'start': self.start,
            'end': self.end,
            # 展馆信息
            'exhihall': self.exhihall.name,
            'city': [self.exhihall.city, self.exhihall.city_py],
            'addr': self.exhihall.addr,
            'notice': self.exhihall.notice,
            # 展览素材
            'theme': self.exhibition.name,
            'thumbnail': up_url(self.exhibition.thumbnail),
            'banner': up_url(self.exhibition.banner),
            'details': [up_url(path) for path in self.exhibition.details],
        }


class Joint(BaseTicket):
    """联票"""

    tids: list[int] = fields.JSONField(default=[], description='展票 ID 列表')  # type: ignore

    thumbnail = fields.CharField(max_length=256, null=False, description='缩略图 URL')
    banner = fields.CharField(max_length=256, null=False, description='概览图 URL')
    details: list[str] = fields.JSONField(default=[], description='详情图 URL 列表')  # type: ignore

    # 声明缓存字段
    exhihall: ExhiHall
    tickets: list[Ticket]

    class Meta:  # type: ignore
        ordering: ClassVar[list[str]] = ['id']

    @classmethod
    async def create(cls, *args, **kwargs):  # type: ignore
        """创建联票"""
        kwargs['catg'] = Catg.joint
        return await super().create(*args, **kwargs)

    @relate_cache
    async def load_tickets(self) -> list[Ticket]:
        """展票类型列表"""
        return await Ticket.in_times(id__in=self.tids, catg=Catg.ticket)

    async def detail(self):
        """展览详情"""
        await self.prefetch()
        return {
            'id': self.id,
            'title': self.title,
            'start': self.start,
            'end': self.end,
            'tickets': [await tk.sample() for tk in self.tickets],
            # 展馆信息
            'exhihall': self.exhihall.name,
            'city': [self.exhihall.city, self.exhihall.city_py],
            'addr': self.exhihall.addr,
            'notice': self.exhihall.notice,
            # 展览素材
            'thumbnail': up_url(self.thumbnail),
            'banner': up_url(self.banner),
            'details': [up_url(path) for path in self.details],
            'prices': self.price_range,
        }
