from typing import ClassVar

from starlette.authentication import BaseUser
from tortoise import fields
from tortoise.transactions import atomic

from config import PAGE_SIZE
from libs.orm import Model


class User(Model, BaseUser):
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=64, description='用户名')
    avatar = fields.CharField(max_length=256, null=True, description='头像')
    openid = fields.CharField(max_length=64, unique=True, description='微信 Open ID')
    union_id = fields.CharField(max_length=64, unique=True, null=True, description='微信的 Union ID')
    phone = fields.CharField(max_length=11, unique=True, null=True, description='手机号')
    is_adm = fields.BooleanField(default=False, description='是否是管理员')

    # 用户状态
    created = fields.DatetimeField(auto_now_add=True, description='创建时间')

    class Meta:  # type: ignore
        table = 'users'
        ordering: ClassVar[list[str]] = ['-id']

    @property
    def is_authenticated(self):
        return True

    @property
    def display_name(self):
        return self.name

    @property
    def identity(self):
        return self.openid

    async def set_phone(self, phone: str | None):
        """设置手机号"""
        if phone is None:
            self.phone = None
            self.is_adm = False
            await self.save()
        else:
            from admin.models import Admin

            phone = phone.strip()
            if self.phone != phone and len(phone) == 11 and phone.isdecimal():
                self.phone = phone
                self.is_adm = await Admin.check_phone(phone)
                await self.save()

    def orders(self, status: str | None = None, limit: int = PAGE_SIZE, offset: int = 0):
        """获取用户订单列表"""
        from apps.order.models import Order

        kwargs = {'uid': self.id, 'status': status} if status else {'uid': self.id}
        query = Order.filter(**kwargs)

        return query.order_by('-created').limit(limit).offset(offset)

    @atomic()
    async def delete(self, *args, **kwargs):
        """删除用户"""
        # 删除用户订单
        from apps.order.models import Order

        await Order.filter(uid=self.id).delete()
        return await super().delete(*args, **kwargs)
