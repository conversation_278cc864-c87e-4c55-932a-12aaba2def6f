import Cache from '../../../utils/cache';
import api from '../../../utils/http';

Page({
  data: {
    activeTab: 'normal', // 可选值：normal, joint
    orderList: [],
    pageNum: 1,
    pageSize: 60,
    isLoadingMore: false,
    hasMoreData: true,
    isAdm: false
  },

  onShow: async function () {
    // 更新 Docker 栏选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: 2 });
    }

    const user = await api.getUser();
    this.setData({ isAdm: user.isAdm, pageNum: 1 });

    if (this.data.isAdm) {
      wx.setNavigationBarTitle({ title: '扫码检票' });
    } else {
      this.loadOrders(true);
    }
  },

  // 主导航切换
  switchMainTab: function (e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab !== this.data.activeTab) {
      this.setData({
        activeTab: tab,
        orderList: [],
        pageNum: 1,
        hasMoreData: true
      });
      this.loadOrders(true);
    }
  },

  // 扫码验票功能
  scanCode: function () {
    wx.scanCode({
      onlyFromCamera: true,
      scanType: ['qrCode'],
      success: (vcode) => {
        this.checkinTicket(vcode.result);
      },
      fail: (err) => {
        wx.showToast({ title: '未识别到二维码', icon: 'error' });
      }
    });
  },

  checkinTicket: function (vcode) {
    try {
      api.checkinOrder(vcode).then((res) => {
        wx.showToast({ title: '验票成功', icon: 'success', duration: 2000 });
        wx.navigateTo({
          url: `/pages/order/detail/detail?orderId=${res.order.id}&checkin=success`
        });
        return;
      });
    } catch (err) {
      wx.showModal({
        title: '验票失败',
        content: err.msg || '网络错误，请重试',
        showCancel: false
      });
    }
  },

  // 加载订单数据
  loadOrders: async function (isRefresh = false) {
    if (this.data.isLoadingMore && !isRefresh) return;
    this.setData({ isLoadingMore: true });
    const { activeTab, pageNum, pageSize } = this.data;
    const is_joint = activeTab === 'joint';
    const status = '待使用';
    const page = Number(pageNum); // 确保 page 为整型
    try {
      const res = await api.getOrderList(is_joint, status, page);
      let orders = Array.isArray(res) ? res : [];
      orders = orders.map((order) => ({
        id: order.id,
        title: order.title,
        status: order.status,
        thumbnail: order.thumbnail,
        grade: order.grade,
        timeslot: order.timeslot,
        amount: order.amount,
        address: order.address,
        quantity: order.quantity
      }));
      const hasMore = orders.length >= pageSize;
      this.setData({
        orderList: isRefresh ? orders : this.data.orderList.concat(orders),
        pageNum: page + 1,
        hasMoreData: hasMore
      });
    } catch (err) {
      wx.showToast({ title: '订单加载失败', icon: 'none' });
    }
    this.setData({ isLoadingMore: false });
    if (isRefresh) wx.stopPullDownRefresh();
  },

  onReachBottom: function () {
    if (this.data.hasMoreData && !this.data.isLoadingMore) {
      this.loadOrders();
    }
  },

  onPullDownRefresh: function () {
    this.setData({ pageNum: 1, hasMoreData: true });
    this.loadOrders(true);
  },

  // 跳转到订单详情页面
  navigateToDetail: function (e) {
    const orderId = e.currentTarget.dataset.orderId;
    if (orderId) {
      wx.navigateTo({
        url: `/pages/order/detail/detail?orderId=${orderId}`
      });
    }
  }
});
