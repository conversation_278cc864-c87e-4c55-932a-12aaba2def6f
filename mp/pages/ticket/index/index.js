import Cache from '../../../utils/cache';
import api from '../../../utils/http';

Page({
  data: {
    theaterEnable: false, // 空间剧场开关

    // 城市选择器相关状态
    selectedCity: '全国',
    showCityPicker: false,
    searchText: '', // 搜索框内容
    cities: [], // 格式: [{ name: '苏州市', pinyin: 'SuZhouShi', initial: 'S' }, ...]
    groupedCities: [], // 按字母分组用于渲染的城市列表
    alphabet: [], // 字母索引
    scrollToLetter: '', // 用于scroll-view的scroll-into-view

    // 展览列表相关数据
    activeNav: 'digital_exhibition', // 当前主导航
    activeSubNavItem: '全部', // 当前子导航标签
    subNavBarItems: [], // 子导航标签列表
    allTickets: [], // 母版数据
    tickets: [], // 展示用的数据

    // 加载状态
    isLoadingMore: false
  },

  onLoad: function (options) {
    // 获取剧场开关状态
    api.getSettings().then((res) => {
      if (res.theater_enable) this.setData({ theaterEnable: true });
    });
  },

  onShow() {
    // 更新 Docker 栏选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: 1 });
    }

    // 获取选中的 Tab
    const tab = Cache.get('selectedTicketTab');
    if (tab && tab !== this.data.activeNav) {
      this.changeNavTo(tab);
    } else if (this.data.tickets.length == 0) {
      this.changeNavTo(this.data.activeNav);
    }
  },

  // 导航栏点击事件
  onNavTap: function (e) {
    // 更新状态
    const navType = e.currentTarget.dataset.type;
    if (this.data.activeNav !== navType) {
      this.changeNavTo(navType);
    }
  },

  // 处理子导航标签数据
  processSubNavItems: function () {
    const { allTickets } = this.data;
    if (!allTickets) return;

    const allExhihalls = Array.from(new Set(allTickets.map((item) => item.exhihall).filter(Boolean)));
    this.setData({ subNavBarItems: ['全部', ...allExhihalls] });
  },

  // 子导航栏点击事件
  onSubNavTap: function (e) {
    const itemName = e.currentTarget.dataset.name;
    if (this.data.activeSubNavItem !== itemName) {
      this.changeSubNavTo(itemName);
    }
  },

  // 切换导航
  changeNavTo: function (navType) {
    if (navType && navType !== this.data.activeNav) {
      this.setData({ activeNav: navType });
    }

    let apiCall;
    this.setData({ isLoadingMore: true });
    if (navType === 'digital_exhibition') {
      apiCall = api.getTicketList(); // 始终获取全国数据
    } else if (navType === 'joint_ticket') {
      apiCall = api.getJointList(); // 始终获取全国数据
    } else {
      this.setData({ allTickets: [], tickets: [], subNavBarItems: [] });
      this.processCities();
      return;
    }

    apiCall.then((result) => {
      this.setData({ allTickets: result });
      this.processCities(); // 根据完整数据生成城市列表
      this.processSubNavItems(); // 根据完整数据生成主题列表
      this.applyFilters(); // 应用当前筛选条件
    });
    this.setData({ isLoadingMore: false });
  },

  // 切换子导航
  changeSubNavTo: function (itemName) {
    this.setData({ activeSubNavItem: itemName });
    this.applyFilters();
  },

  // 从 allTickets 中提取城市信息，并生成排序和分组后的列表
  processCities: function () {
    const { allTickets } = this.data;
    if (!allTickets || allTickets.length === 0) {
      this.setData({ cities: [], groupedCities: [], alphabet: [] });
      return;
    }

    // 1. 提取并去重城市
    const citySet = new Map();
    allTickets.forEach((ticket) => {
      if (ticket.city && ticket.city.length === 2) {
        const [name, pinyin] = ticket.city;
        if (!citySet.has(name)) {
          citySet.set(name, {
            name: name,
            pinyin: pinyin,
            initial: pinyin.charAt(0).toUpperCase()
          });
        }
      }
    });

    // 2. 转换并按拼音排序
    let cities = [...citySet.values()].sort((a, b) => {
      return a.pinyin.localeCompare(b.pinyin);
    });

    // 3. 添加“全部城市”到顶部
    this.setData({ cities: cities });

    // 4. 按首字母分组
    const grouped = {};
    cities.forEach((city) => {
      const letter = city.initial;
      if (!grouped[letter]) {
        grouped[letter] = [];
      }
      grouped[letter].push(city);
    });

    const groupedCities = Object.keys(grouped)
      .sort()
      .map((letter) => ({
        letter: letter,
        cities: grouped[letter]
      }));

    // 5. 生成字母索引并更新数据
    const alphabet = groupedCities.map((group) => group.letter);
    this.setData({
      groupedCities: groupedCities,
      alphabet: alphabet
    });
  },

  openCityPicker: function () {
    this.setData({ showCityPicker: true });
  },

  closeCityPicker: function () {
    this.setData({ showCityPicker: false, searchText: '' });
    // 搜索状态重置
    this.onSearchInput({ detail: { value: '' } });
  },

  onSearchInput: function (e) {
    const searchText = e.detail.value.trim().toLowerCase();
    this.setData({ searchText: searchText });

    const { cities } = this.data;
    if (!searchText) {
      // 如果搜索框为空，则恢复原始分组
      this.processCities();
      return;
    }

    // 从完整城市列表（未分组）中过滤
    const filteredCities = cities.filter(
      (city) => city.name.toLowerCase().includes(searchText) || city.pinyin.toLowerCase().includes(searchText)
    );

    // 对过滤结果重新分组
    const grouped = {};
    filteredCities.forEach((city) => {
      const letter = city.initial;
      if (!grouped[letter]) {
        grouped[letter] = [];
      }
      grouped[letter].push(city);
    });

    const filteredGroupedCities = Object.keys(grouped)
      .sort()
      .map((letter) => ({
        letter: letter,
        cities: grouped[letter]
      }));

    this.setData({ groupedCities: filteredGroupedCities });
  },

  // 城市选择事件
  onCityTap: function (e) {
    const { cityname } = e.currentTarget.dataset;
    const currentCity = cityname === '全部城市' ? '全国' : cityname;

    this.setData({
      selectedCity: currentCity,
      activeSubNavItem: '全部' // 选择新城市后，重置子导航
    });

    this.applyFilters();
    this.closeCityPicker();
  },

  // 根据当前选择的城市和展馆，过滤 allTickets 并更新 tickets
  applyFilters: function () {
    let { allTickets, selectedCity, activeSubNavItem } = this.data;
    let filteredList = allTickets;

    // 1. 按城市过滤
    if (selectedCity !== '全国') {
      filteredList = filteredList.filter((ticket) => ticket.city && ticket.city[0] === selectedCity);
    }

    // 2. 按展馆过滤
    if (activeSubNavItem !== '全部') {
      filteredList = filteredList.filter((tk) => tk.exhihall === activeSubNavItem);
    }

    this.setData({ tickets: filteredList });
  },

  scrollToLetter: function (e) {
    const letter = e.currentTarget.dataset.letter;
    this.setData({ scrollToLetter: 'letter-' + letter });
  },

  goToDetail: function (e) {
    if (this.data.activeNav === 'digital_exhibition') {
      const ticketId = e.currentTarget.dataset.id;
      wx.navigateTo({ url: `/pages/ticket/exhib/exhib?id=${ticketId}` });
    } else if (this.data.activeNav === 'space_theater') {
      wx.showToast({
        title: '空间剧场暂未开放，敬请期待',
        icon: 'none'
      });
    } else {
      const jointId = e.currentTarget.dataset.id;
      wx.navigateTo({ url: `/pages/ticket/joint/joint?id=${jointId}` });
    }
  }
});
