<view class="container" wx:if="{{jointDetail}}">
  <navbar />

  <!-- Banner -->
  <image class="main-banner"
         src="{{jointDetail.banner}}"
         mode="aspectFill"></image>

  <view class="main-card">
    <view class="section">
      <!-- 票务详情 -->
      <view class="ticket-info-section">
        <view class="ticket-title">{{jointDetail.title}}</view>
        <view class="ticket-address">地址: {{jointDetail.addr}}</view>
        <view class="ticket-price">
          <text class="price-symbol">¥</text>
          <text class="price-value">{{jointDetail.prices[0]}}</text>
          <text wx:if="{{jointDetail.prices.length > 1 && jointDetail.prices[0] !== jointDetail.prices[1]}}"
                class="price-value">~{{jointDetail.prices[1]}}</text>
          <text wx:if="{{jointDetail.prices.length == 1}}" class="price-value">起</text>
        </view>
      </view>

      <!-- 退票政策 -->
      <view class="features-section" bindtap="goToFeatures">
        <view class="feature-list">
          <view class="feature-item">
            <image class="feature-icon" src="{{ features.refundable ? '/assets/icons/check-solid.svg' : '/assets/icons/exclamation-solid.svg' }}"></image>
            <text class="feature-text">{{ features.refundable ? '无忧退票' : '有条件退票' }}</text>
          </view>
          <view class="feature-item">
            <image class="feature-icon" src="{{ features.source === 'office' ? '/assets/icons/check-solid.svg' : '/assets/icons/exclamation-solid.svg' }}"></image>
            <text class="feature-text">{{ features.source === 'office' ? '官方票' : '第三方票' }}</text>
          </view>
          <view class="feature-item">
            <image class="feature-icon" src="{{ features.electronic ? '/assets/icons/check-solid.svg' : '/assets/icons/exclamation-solid.svg' }}"></image>
            <text class="feature-text">{{ features.electronic ? '电子票' : '纸质票' }}</text>
          </view>
          <view class="feature-item">
            <image class="feature-icon" src="{{ features.invoice ? '/assets/icons/check-solid.svg' : '/assets/icons/exclamation-solid.svg' }}"></image>
            <text class="feature-text">{{ features.invoice ? '客服开发票' : '无法开发票' }}</text>
          </view>
        </view>
        <image class="arrow-icon" src="/assets/icons/right-solid.svg"></image>
      </view>

      <!-- 演出详情标题 -->
      <view class="detail-title-section">
        <view class="section-title">联票详情</view>
      </view>
    </view>

    <view wx:if="{{jointDetail.tickets.length > 0}}">
      <list-item
          wx:for="{{jointDetail.tickets}}"
          wx:key="id"
          thumbnail="{{item.thumbnail}}"
          title="{{item.title}}"
          price="{{item.prices[1]}}"
          priceSuffix=""
          data-id="{{item.id}}"
          bindtap="goToTicketDetail"
      ></list-item>
    </view>
    <view wx:else class="empty">
      <image class="empty-image" src="/assets/empty.png" mode="widthFix"></image>
      <text>未配置联票</text>
    </view>

    <view wx:if="{{ jointDetail.details }}">
      <image wx:for="{{jointDetail.details}}" wx:key="*this" src="{{item}}" class="detail-image" mode="widthFix"></image>
    </view>
  </view>

  <!-- 底部购票按钮 -->
  <view class="bottom-actions">
    <button class="btn ar-btn"
            bindtap="goToTicketAR">
      <image class="ar-icon" src="/assets/icons/ar.svg" />
    </button>
    <button class="btn purchase-btn"
            bindtap="goToJointPurchase">立即购票</button>
  </view>
</view>

<!-- 加载提示或错误提示 -->
<view class="loading-container" wx:else>
  <text>加载中...</text>
</view>

<!-- Notice Dialog Component -->
<notice-dialog
  id="noticeDialog"
  show="{{showNoticeDialog}}"
  title="入馆须知"
  notice="{{jointDetail.notice || ''}}"
  bind:confirm="onNoticeDialogConfirm">
</notice-dialog>
